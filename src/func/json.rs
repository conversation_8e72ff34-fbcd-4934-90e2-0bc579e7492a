//! Generic JSON functions that work across different database backends.

use crate::*;
use std::borrow::Cow;

// Re-export the types from the PostgreSQL extension for convenience
#[cfg(feature = "backend-postgres")]
pub use crate::extension::postgres::func::json_value::{
    Builder as Json<PERSON><PERSON>ueBuilder, OnEmpty, OnError,
};

/// Generic JSON function implementations.
#[derive(Debug)]
pub struct JsonFunc;

impl JsonFunc {
    /// Create a generic `JSON_VALUE` function builder that works with any QueryBuilder.
    ///
    /// Returns the result of applying the SQL/JSON path_expression to the context_item.
    /// Only use JSON_VALUE() if the extracted value is expected to be a single SQL/JSON scalar item.
    /// Supports RETURNING, ON EMPTY, and ON ERROR clauses.
    ///
    /// # Examples
    ///
    /// Basic usage with PostgreSQL:
    /// ```
    /// use sea_query::{func::json::*, tests_cfg::*, *};
    ///
    /// let query = Query::select()
    ///     .expr(
    ///         JsonFunc::json_value(Expr::val(r#""123.45""#), "$")
    ///             .returning("float".into())
    ///             .build_with_query_builder(&PostgresQueryBuilder),
    ///     )
    ///     .to_owned();
    ///
    /// assert_eq!(
    ///     query.to_string(PostgresQueryBuilder),
    ///     r#"SELECT JSON_VALUE(E'\"123.45\"' '$' RETURNING float)"#
    /// );
    /// ```
    ///
    /// Usage with MySQL:
    /// ```
    /// use sea_query::{func::json::*, tests_cfg::*, *};
    ///
    /// let query = Query::select()
    ///     .expr(
    ///         JsonFunc::json_value(Expr::val(r#""123.45""#), "$")
    ///             .returning("DECIMAL(10,2)".into())
    ///             .build_with_query_builder(&MysqlQueryBuilder),
    ///     )
    ///     .to_owned();
    ///
    /// assert_eq!(
    ///     query.to_string(MysqlQueryBuilder),
    ///     r#"SELECT JSON_VALUE('\"123.45\"' '$' RETURNING DECIMAL(10,2))"#
    /// );
    /// ```
    #[cfg(feature = "backend-postgres")]
    pub fn json_value<T, P>(context_item: T, path_expression: P) -> JsonValueBuilder
    where
        T: Into<Expr>,
        P: Into<Cow<'static, str>>,
    {
        JsonValueBuilder {
            context_item: context_item.into(),
            path_expression: path_expression.into(),
            passing: Vec::new(),
            returning: None,
            on_empty: None,
            on_error: None,
        }
    }
}
