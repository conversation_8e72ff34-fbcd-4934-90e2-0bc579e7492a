use std::borrow::Cow;

use crate::*;

/// Builder for JSON_QUERY function
#[derive(Debug, Clone)]
pub struct Builder {
    pub(crate) context_item: Expr,
    pub(crate) path_expression: Cow<'static, str>,
    pub(crate) passing: Vec<(Value, Cow<'static, str>)>,
    pub(crate) returning: Option<Cow<'static, str>>,
    pub(crate) wrapper: Option<Wrapper>,
    pub(crate) quotes: Option<Quotes>,
    pub(crate) on_empty: Option<OnEmpty>,
    pub(crate) on_error: Option<OnError>,
}

#[derive(Debug, Clone)]
pub enum Wrapper {
    Without,
    WithConditional,
    WithUnconditional,
}

#[derive(Debug, Clone)]
pub enum Quotes {
    Keep,
    Omit,
}

#[derive(Debug, Clone)]
pub enum OnEmpty {
    Error,
    Null,
    EmptyArray,
    EmptyObject,
    Default(Expr),
}

#[derive(Debug, Clone)]
pub enum OnError {
    Error,
    Null,
    EmptyArray,
    EmptyObject,
    Default(Expr),
}

impl Builder {
    /// Add PASSING parameters
    pub fn passing(mut self, passing: Vec<(Value, Cow<'static, str>)>) -> Self {
        self.passing = passing;
        self
    }

    /// Set RETURNING clause
    pub fn returning(mut self, returning: Cow<'static, str>) -> Self {
        self.returning = Some(returning);
        self
    }

    /// Set WRAPPER clause
    pub fn wrapper(mut self, wrapper: Wrapper) -> Self {
        self.wrapper = Some(wrapper);
        self
    }

    /// Set QUOTES clause
    pub fn quotes(mut self, quotes: Quotes) -> Self {
        self.quotes = Some(quotes);
        self
    }

    /// Set ON EMPTY clause
    pub fn on_empty(mut self, on_empty: OnEmpty) -> Self {
        self.on_empty = Some(on_empty);
        self
    }

    /// Set ON ERROR clause
    pub fn on_error(mut self, on_error: OnError) -> Self {
        self.on_error = Some(on_error);
        self
    }

    pub fn build(self) -> FunctionCall {
        let mut buf = String::with_capacity(50);

        PostgresQueryBuilder.prepare_simple_expr(&self.context_item, &mut buf);
        buf.write_str(" '").unwrap();
        buf.write_str(&self.path_expression).unwrap();
        buf.write_str("'").unwrap();

        // PASSING clause
        let mut piter = self.passing.into_iter();
        join_io!(
            piter,
            value_as,
            first {
                buf.write_str(" PASSING ").unwrap();
            },
            join {
                buf.write_str(", ").unwrap();
            },
            do {
                PostgresQueryBuilder.prepare_value(value_as.0, &mut buf);
                buf.write_str(" AS ").unwrap();
                buf.write_str(&value_as.1).unwrap();
            }
        );

        // RETURNING clause
        if let Some(returning) = self.returning {
            buf.write_str(" RETURNING ").unwrap();
            buf.write_str(&returning).unwrap();
        }

        // WRAPPER clause
        if let Some(wrapper) = self.wrapper {
            match wrapper {
                Wrapper::Without => buf.write_str(" WITHOUT WRAPPER").unwrap(),
                Wrapper::WithConditional => buf.write_str(" WITH CONDITIONAL WRAPPER").unwrap(),
                Wrapper::WithUnconditional => buf.write_str(" WITH UNCONDITIONAL WRAPPER").unwrap(),
            }
        }

        // QUOTES clause
        if let Some(quotes) = self.quotes {
            match quotes {
                Quotes::Keep => buf.write_str(" KEEP QUOTES").unwrap(),
                Quotes::Omit => buf.write_str(" OMIT QUOTES").unwrap(),
            }
        }

        // ON EMPTY clause
        if let Some(on_empty) = self.on_empty {
            match on_empty {
                OnEmpty::Error => buf.write_str(" ERROR ON EMPTY").unwrap(),
                OnEmpty::Null => buf.write_str(" NULL ON EMPTY").unwrap(),
                OnEmpty::EmptyArray => buf.write_str(" EMPTY ARRAY ON EMPTY").unwrap(),
                OnEmpty::EmptyObject => buf.write_str(" EMPTY OBJECT ON EMPTY").unwrap(),
                OnEmpty::Default(expr) => {
                    buf.write_str(" DEFAULT ").unwrap();
                    PostgresQueryBuilder.prepare_simple_expr(&expr, &mut buf);
                    buf.write_str(" ON EMPTY").unwrap();
                }
            }
        }

        // ON ERROR clause
        if let Some(on_error) = self.on_error {
            match on_error {
                OnError::Error => buf.write_str(" ERROR ON ERROR").unwrap(),
                OnError::Null => buf.write_str(" NULL ON ERROR").unwrap(),
                OnError::EmptyArray => buf.write_str(" EMPTY ARRAY ON ERROR").unwrap(),
                OnError::EmptyObject => buf.write_str(" EMPTY OBJECT ON ERROR").unwrap(),
                OnError::Default(expr) => {
                    buf.write_str(" DEFAULT ").unwrap();
                    PostgresQueryBuilder.prepare_simple_expr(&expr, &mut buf);
                    buf.write_str(" ON ERROR").unwrap();
                }
            }
        }

        FunctionCall::new(Func::Custom("JSON_QUERY".into())).arg(Expr::Custom(buf))
    }
}

impl PgFunc {
    /// Create a `JSON_QUERY` function builder. Postgres only.
    ///
    /// Returns the result of applying the SQL/JSON path_expression to the context_item.
    /// Supports various options like RETURNING, WRAPPER, QUOTES, ON EMPTY, and ON ERROR clauses.
    ///
    /// # Examples
    ///
    /// Basic usage:
    /// ```
    /// use sea_query::{tests_cfg::*, *};
    /// use sea_query::extension::postgres::*;
    ///
    /// let query = Query::select()
    ///     .expr(PgFunc::json_query(
    ///         Expr::val(r#"[1,[2,3],null]"#),
    ///         "lax $[*][$off]"
    ///     )
    ///     .passing(vec![(1.into(), "off".into())])
    ///     .wrapper(JsonQueryWrapper::WithConditional)
    ///     .build())
    ///     .to_owned();
    ///
    /// assert_eq!(
    ///     query.to_string(PostgresQueryBuilder),
    ///     r#"SELECT JSON_QUERY('[1,[2,3],null]' 'lax $[*][$off]' PASSING 1 AS off WITH CONDITIONAL WRAPPER)"#
    /// );
    /// ```
    ///
    /// With OMIT QUOTES:
    /// ```
    /// use sea_query::extension::postgres::*;
    /// use sea_query::{tests_cfg::*, *};
    ///
    /// let query = Query::select()
    ///     .expr(
    ///         PgFunc::json_query(Expr::val(r#"{"a": "[1, 2]"}"#), "lax $.a")
    ///             .quotes(JsonQueryQuotes::Omit)
    ///             .build(),
    ///     )
    ///     .to_owned();
    ///
    /// assert_eq!(
    ///     query.to_string(PostgresQueryBuilder),
    ///     r#"SELECT JSON_QUERY(E'{\"a\": \"[1, 2]\"}' 'lax $.a' OMIT QUOTES)"#
    /// );
    /// ```
    pub fn json_query<T, P>(context_item: T, path_expression: P) -> Builder
    where
        T: Into<Expr>,
        P: Into<Cow<'static, str>>,
    {
        Builder {
            context_item: context_item.into(),
            path_expression: path_expression.into(),
            passing: Vec::new(),
            returning: None,
            wrapper: None,
            quotes: None,
            on_empty: None,
            on_error: None,
        }
    }
}
