//! Example demonstrating the generic JSON_VALUE function that works across different database backends.

use sea_query::*;
use sea_query::extension::postgres::*;
use sea_query::func::json::*;

fn main() {
    // Example JSON data
    let json_data = r#"{"name": "<PERSON>", "age": 30, "salary": 75000.50}"#;
    
    println!("=== Generic JSON_VALUE Function Examples ===\n");
    
    // PostgreSQL example
    let pg_query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(json_data), "$.name")
                .returning("text".into())
                .build_with_query_builder(&PostgresQueryBuilder)
        )
        .to_owned();
    
    println!("PostgreSQL:");
    println!("{}\n", pg_query.to_string(PostgresQueryBuilder));
    
    // MySQL example
    let mysql_query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(json_data), "$.salary")
                .returning("DECIMAL(10,2)".into())
                .build_with_query_builder(&MysqlQueryBuilder)
        )
        .to_owned();
    
    println!("MySQL:");
    println!("{}\n", mysql_query.to_string(MysqlQueryBuilder));
    
    // SQLite example
    let sqlite_query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(json_data), "$.age")
                .returning("INTEGER".into())
                .build_with_query_builder(&SqliteQueryBuilder)
        )
        .to_owned();
    
    println!("SQLite:");
    println!("{}\n", sqlite_query.to_string(SqliteQueryBuilder));
    
    // Example with error handling
    let error_handling_query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(json_data), "$.nonexistent")
                .returning("text".into())
                .on_error(OnError::Default(Expr::val("N/A")))
                .build_with_query_builder(&PostgresQueryBuilder)
        )
        .to_owned();
    
    println!("With error handling (PostgreSQL):");
    println!("{}\n", error_handling_query.to_string(PostgresQueryBuilder));
    
    // Example with PASSING parameters
    let passing_query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#"[10, 20, 30]"#), "strict $[$idx]")
                .passing(1, "idx")
                .returning("integer".into())
                .build_with_query_builder(&PostgresQueryBuilder)
        )
        .to_owned();
    
    println!("With PASSING parameters (PostgreSQL):");
    println!("{}\n", passing_query.to_string(PostgresQueryBuilder));
    
    // Backward compatibility - original PostgreSQL-specific API still works
    let pg_specific_query = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(json_data), "$.name")
                .returning("text".into())
                .build()
        )
        .to_owned();
    
    println!("Backward compatibility (PostgreSQL-specific API):");
    println!("{}", pg_specific_query.to_string(PostgresQueryBuilder));
}
