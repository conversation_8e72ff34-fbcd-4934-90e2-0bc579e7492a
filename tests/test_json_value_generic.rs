use sea_query::extension::postgres::*;
use sea_query::func::json::*;
use sea_query::*;

#[test]
fn test_json_value_generic_postgres() {
    let query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("float".into())
                .build_with_query_builder(&PostgresQueryBuilder),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'\"123.45\"' '$' RETURNING float)"#
    );
}

#[test]
fn test_json_value_generic_mysql() {
    let query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("DECIMAL(10,2)".into())
                .build_with_query_builder(&MysqlQueryBuilder),
        )
        .to_owned();

    assert_eq!(
        query.to_string(MysqlQueryBuilder),
        r#"SELECT JSON_VALUE('\"123.45\"' '$' RETURNING DECIMAL(10,2))"#
    );
}

#[test]
fn test_json_value_generic_sqlite() {
    let query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("REAL".into())
                .build_with_query_builder(&SqliteQueryBuilder),
        )
        .to_owned();

    assert_eq!(
        query.to_string(SqliteQueryBuilder),
        r#"SELECT JSON_VALUE('"123.45"' '$' RETURNING REAL)"#
    );
}

#[test]
fn test_json_value_backward_compatibility() {
    // Test that the original PostgreSQL-specific API still works
    let query = Query::select()
        .expr(
            PgFunc::json_value(Expr::val(r#""123.45""#), "$")
                .returning("float".into())
                .build(),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE(E'\"123.45\"' '$' RETURNING float)"#
    );
}

#[test]
fn test_json_value_with_passing_generic() {
    let query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#"[1,2]"#), "strict $[$off]")
                .passing(1, "off")
                .build_with_query_builder(&PostgresQueryBuilder),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('[1,2]' 'strict $[$off]' PASSING 1 AS off)"#
    );
}

#[test]
fn test_json_value_with_on_error_generic() {
    let query = Query::select()
        .expr(
            JsonFunc::json_value(Expr::val(r#"[1,2]"#), "strict $[*]")
                .on_error(OnError::Default(Expr::val(9)))
                .build_with_query_builder(&PostgresQueryBuilder),
        )
        .to_owned();

    assert_eq!(
        query.to_string(PostgresQueryBuilder),
        r#"SELECT JSON_VALUE('[1,2]' 'strict $[*]' DEFAULT 9 ON ERROR)"#
    );
}
